<template>
  <!-- Voice recorder overlay -->
  <div v-if="voiceState.isRecording || voiceState.isTranscribing" class="w-full md:px-2">
    <VoiceRecorder
      :state="voiceState"
      @stop="stopRecording"
      class="mx-auto w-full max-w-4xl"
    />
  </div>

  <!-- Main input container -->
  <div
    v-show="!voiceState.isRecording && !voiceState.isTranscribing"
    class="w-full px-1"
    @drop="handleDrop"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
  >
    <div
      :class="cn(
        'mx-auto w-full max-w-4xl rounded-t-lg border-2 border-input bg-background/80 p-2 shadow-xs backdrop-blur-lg md:rounded-lg dark:bg-input/70',
        dragActive && 'rounded-lg ring-2 ring-primary ring-offset-2'
      )"
    >
      <!-- File previews -->
      <div v-if="uploadedFiles.length > 0" class="flex flex-wrap gap-2 pb-3">
        <div
          v-for="file in uploadedFiles"
          :key="file.key"
          class="relative flex items-center gap-2 rounded-md bg-muted p-2"
        >
          <component :is="getFileIcon(file.fileType)" class="h-4 w-4" />
          <span class="text-sm">{{ file.fileName }}</span>
          <Button
            @click="removeFile(file.key)"
            variant="ghost"
            size="icon"
            class="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
          >
            <X class="h-3 w-3" />
          </Button>
        </div>
      </div>

      <!-- Textarea -->
      <Textarea
        ref="textareaRef"
        v-model="inputValue"
        :placeholder="isImageModel ? 'Describe the image you want to generate...' : 'Ask me anything...'"
        class="min-h-[44px] w-full resize-none border-none bg-transparent text-foreground shadow-none outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
        rows="1"
        :disabled="status === 'loading'"
        @keydown="handleKeyDown"
        @input="handleInput"
      />

      <!-- Actions row -->
      <div class="flex items-center justify-between pt-2">
        <!-- Left side - Model selector and tools -->
        <div class="flex items-center gap-2">
          <ModelSelector />
          
          <!-- Image size selector for image models -->
          <Select v-if="isImageModel" v-model="selectedImageSize">
            <SelectTrigger class="h-8 w-auto bg-secondary/70 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="size in supportedImageSizes"
                :key="size"
                :value="size"
              >
                {{ size }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Right side - Action buttons -->
        <div class="flex items-center gap-2">
          <!-- File upload button -->
          <Tooltip v-if="!isImageModel">
            <TooltipTrigger as-child>
              <Button
                type="button"
                variant="ghost"
                @click="() => uploadInputRef?.click()"
                class="flex size-8 cursor-pointer items-center justify-center gap-1 rounded-md bg-secondary/70 text-foreground backdrop-blur-lg hover:bg-secondary/80"
              >
                <input
                  ref="uploadInputRef"
                  type="file"
                  multiple
                  @change="handleFileChange"
                  class="hidden"
                  :accept="getFileAcceptAttribute()"
                />
                <Loader2 v-if="uploading" class="size-4 animate-spin" />
                <Paperclip v-else class="-rotate-45 size-4 hover:text-primary" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Attach files</TooltipContent>
          </Tooltip>

          <!-- Voice recording button -->
          <Tooltip v-if="!isImageModel">
            <TooltipTrigger as-child>
              <Button
                type="button"
                variant="ghost"
                @click="startRecording"
                class="flex size-8 cursor-pointer items-center justify-center gap-1 rounded-md bg-secondary/70 text-foreground backdrop-blur-lg hover:bg-secondary/80"
                :disabled="voiceState.isRecording || voiceState.isTranscribing"
              >
                <Mic class="size-4 hover:text-primary" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Voice input</TooltipContent>
          </Tooltip>

          <!-- Submit button -->
          <Button
            @click="handleSubmit"
            :disabled="status === 'loading' || (!inputValue.trim() && uploadedFiles.length === 0)"
            class="size-8 rounded-md"
            size="icon"
          >
            <Loader2 v-if="status === 'loading'" class="size-4 animate-spin" />
            <ArrowUp v-else class="size-4" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useChatStore, type UploadedFile } from '@/stores/chat'
import { useModelStore } from '@/stores/model'
import { cn } from '@/lib/utils'
import { nanoid } from 'nanoid'
import {
  ArrowUp,
  Loader2,
  Mic,
  Paperclip,
  X,
  FileText,
  Image as ImageIcon,
  Code
} from 'lucide-vue-next'
import type { VoiceRecorderState } from './VoiceRecorder.vue'

interface Props {
  status?: 'loading' | 'error' | 'idle'
}

interface Emits {
  (e: 'submit', input?: string, files?: UploadedFile[]): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const chatStore = useChatStore()
const modelStore = useModelStore()

const textareaRef = ref<HTMLTextAreaElement>()
const uploadInputRef = ref<HTMLInputElement>()
const inputValue = ref('')
const dragActive = ref(false)

// Voice recording state (simplified)
const voiceState = ref<VoiceRecorderState>({
  isRecording: false,
  isTranscribing: false,
  recordingDuration: 0,
  audioLevel: 0
})

// Computed properties
const uploadedFiles = computed(() => chatStore.uploadedFiles)
const uploading = computed(() => chatStore.uploading)
const isImageModel = computed(() => modelStore.isImageModel)
const selectedImageSize = computed({
  get: () => modelStore.selectedImageSize,
  set: (value) => value && modelStore.setSelectedImageSize(value)
})

const supportedImageSizes = computed(() => {
  const model = modelStore.selectedModelData
  return model?.supportedImageSizes || ['1024x1024']
})

// File handling
const getFileIcon = (fileType: string) => {
  if (fileType.startsWith('image/')) return ImageIcon
  if (fileType.includes('code') || fileType.includes('text')) return Code
  return FileText
}

const getFileAcceptAttribute = () => {
  return 'image/*,text/*,.pdf,.doc,.docx,.txt,.md,.json,.csv'
}

const handleFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files) return

  chatStore.setUploading(true)

  try {
    for (const file of Array.from(files)) {
      const uploadedFile: UploadedFile = {
        key: nanoid(),
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        uploadedAt: Date.now()
      }
      chatStore.addUploadedFile(uploadedFile)
    }
  } finally {
    chatStore.setUploading(false)
    target.value = '' // Reset input
  }
}

const removeFile = (key: string) => {
  chatStore.removeUploadedFile(key)
}

// Drag and drop
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  dragActive.value = true
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  dragActive.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  dragActive.value = false

  const files = e.dataTransfer?.files
  if (files) {
    // Simulate file input change
    const event = { target: { files } } as any
    handleFileChange(event)
  }
}

// Voice recording (simplified)
const startRecording = () => {
  voiceState.value.isRecording = true
  // In a real app, you'd start actual recording here
}

const stopRecording = () => {
  voiceState.value.isRecording = false
  voiceState.value.isTranscribing = true

  // Simulate transcription
  setTimeout(() => {
    voiceState.value.isTranscribing = false
    inputValue.value += ' [Voice input transcribed]'
  }, 2000)
}

// Input handling
const handleInput = () => {
  // Auto-resize textarea
  if (textareaRef.value) {
    textareaRef.value.style.height = 'auto'
    textareaRef.value.style.height = textareaRef.value.scrollHeight + 'px'
  }

  // Save to localStorage
  if (process.client) {
    localStorage.setItem('user-input', inputValue.value)
  }
}

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault()
    handleSubmit()
  }
}

const handleSubmit = () => {
  if (!inputValue.value.trim() && uploadedFiles.value.length === 0) return

  emit('submit', inputValue.value, [...uploadedFiles.value])

  // Clear input and files
  inputValue.value = ''
  chatStore.uploadedFiles.forEach(file => chatStore.removeUploadedFile(file.key))

  if (process.client) {
    localStorage.removeItem('user-input')
  }

  // Reset textarea height
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.style.height = 'auto'
    }
  })
}

// Initialize input from localStorage
onMounted(() => {
  if (process.client) {
    const saved = localStorage.getItem('user-input')
    if (saved) {
      inputValue.value = saved
    }
  }
})
</script>
