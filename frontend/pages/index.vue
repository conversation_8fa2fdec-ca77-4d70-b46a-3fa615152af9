<template>
  <div class="min-h-screen bg-background text-foreground">
    <NuxtRouteAnnouncer />
    
    <!-- Main content grid -->
    <main class="grid min-h-screen grid-cols-1 lg:grid-cols-2">
      <!-- Left side - Background Image -->
      <div class="hidden bg-[url('/bg-light.jpg')] bg-center bg-cover bg-no-repeat lg:block dark:bg-[url('/bg-night.jpg')]" />
      
      <!-- Right side - Content -->
      <div class="relative flex flex-col items-center justify-center gap-4 p-4 sm:p-6 md:p-8">
        <!-- Theme switcher in top right -->
        <div class="absolute top-4 right-4 sm:top-6 sm:right-6">
          <ThemeSwitcher />
        </div>
        
        <!-- Main content -->
        <div class="flex w-full max-w-sm items-center justify-center gap-4 sm:max-w-md lg:max-w-lg">
          <SignupMessagePrompt />
        </div>
        
        <!-- Footer content -->
        <div class="absolute right-4 bottom-4 left-4 flex flex-col items-center gap-2 sm:right-6 sm:bottom-6 sm:left-6">
          <p class="text-xs text-muted-foreground text-center">
            By continuing, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
// Page meta for SEO
definePageMeta({
  title: 'Welcome to intern3.chat',
  description: 'AI-powered chat application'
})
</script>
