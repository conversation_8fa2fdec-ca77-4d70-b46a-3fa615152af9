<template>
  <div class="min-h-screen bg-background text-foreground">
    <NuxtRouteAnnouncer />

    <!-- Header with theme switcher -->
    <header class="sticky top-0 z-50 border-b bg-background/80 backdrop-blur-lg">
      <div class="flex h-16 items-center justify-between px-4 sm:px-6">
        <div class="flex items-center gap-2">
          <div class="h-8 w-8 rounded-full border">
            <Logo />
          </div>
          <span class="font-semibold text-lg">intern3.chat</span>
        </div>
        <ThemeSwitcher />
      </div>
    </header>

    <!-- Main chat interface -->
    <main class="h-[calc(100vh-64px)]">
      <!-- Show auth prompt if not authenticated, otherwise show chat -->
      <div v-if="!isAuthenticated" class="grid h-full grid-cols-1 lg:grid-cols-2">
        <!-- Left side - Background Image -->
        <div class="hidden bg-[url('/bg-light.jpg')] bg-center bg-cover bg-no-repeat lg:block dark:bg-[url('/bg-night.jpg')]" />

        <!-- Right side - Auth Content -->
        <div class="relative flex flex-col items-center justify-center gap-4 p-4 sm:p-6 md:p-8">
          <div class="flex w-full max-w-sm items-center justify-center gap-4 sm:max-w-md lg:max-w-lg">
            <SignupMessagePrompt />
          </div>

          <!-- Footer content -->
          <div class="absolute right-4 bottom-4 left-4 flex flex-col items-center gap-2 sm:right-6 sm:bottom-6 sm:left-6">
            <p class="text-xs text-muted-foreground text-center">
              By continuing, you agree to our Terms of Service and Privacy Policy
            </p>
          </div>
        </div>
      </div>

      <!-- Chat interface for authenticated users -->
      <Chat v-else />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'

// Page meta for SEO
definePageMeta({
  title: 'Welcome to intern3.chat',
  description: 'AI-powered chat application'
})

// Mock authentication state - in real app this would come from auth store/composable
const isAuthenticated = ref(false)

// Initialize theme
const themeStore = useThemeStore()

onMounted(() => {
  themeStore.initializeTheme()

  // Mock authentication check - in real app you'd check actual auth state
  // For demo purposes, let's show the chat interface directly
  // Set to false to see the auth prompt, true to see the chat
  isAuthenticated.value = true
})
</script>
