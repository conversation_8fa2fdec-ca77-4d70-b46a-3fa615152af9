{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"clsx": "^2.1.1", "nuxt": "^4.1.0", "shadcn-vue": "^2.2.0", "tailwind-merge": "^3.3.1", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.13", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "tailwindcss-animate": "^1.0.7"}}